/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* 主要内容区域 */
.main-content {
    display: grid;
    grid-template-columns: 300px 1fr 300px;
    gap: 20px;
    margin-bottom: 30px;
}

/* 控制面板 */
.control-panel {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.status-info {
    margin-bottom: 20px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.label {
    font-weight: 600;
    color: #555;
}

.status {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.9rem;
    font-weight: 500;
}

.status.connected {
    background: #d4edda;
    color: #155724;
}

.status.loading {
    background: #fff3cd;
    color: #856404;
}

.status.ready {
    background: #d1ecf1;
    color: #0c5460;
}

.status.error {
    background: #f8d7da;
    color: #721c24;
}

/* 按钮样式 */
.action-controls {
    margin-bottom: 20px;
}

.btn {
    width: 100%;
    padding: 12px;
    margin-bottom: 10px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-primary:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #5a6268;
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    border: 2px solid #667eea;
    color: #667eea;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
}

/* 检测模式选择器 */
.detection-mode {
    margin-bottom: 20px;
}

.detection-mode label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #555;
}

/* 显示控制 */
.display-controls {
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

/* 切换开关样式 */
.toggle-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
}

.toggle-container input[type="checkbox"] {
    display: none;
}

.toggle-slider {
    position: relative;
    width: 50px;
    height: 24px;
    background: #ccc;
    border-radius: 24px;
    transition: background 0.3s ease;
    margin-right: 12px;
}

.toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: transform 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.toggle-container input[type="checkbox"]:checked + .toggle-slider {
    background: #667eea;
}

.toggle-container input[type="checkbox"]:checked + .toggle-slider::before {
    transform: translateX(26px);
}

.toggle-label {
    font-weight: 500;
    color: #495057;
    font-size: 0.95rem;
}

/* 动作选择器 */
.pose-selector {
    margin-top: 20px;
}

.pose-selector label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #555;
}

.select {
    width: 100%;
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    background: white;
    cursor: pointer;
}

.select:focus {
    outline: none;
    border-color: #667eea;
}

/* 视频容器 */
.video-container {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
}

#video-wrapper {
    position: relative;
    background: #f8f9fa;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 20px;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 姿态信息 */
.pose-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.pose-guide, .pose-feedback {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
}

.pose-guide h3, .pose-feedback h3 {
    margin-bottom: 10px;
    color: #495057;
    font-size: 1.1rem;
}

#pose-instructions, #pose-feedback {
    color: #6c757d;
    line-height: 1.5;
}

/* 评分面板 */
.scoring-panel {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.scoring-panel:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.15);
}

.score-display {
    text-align: center;
    margin-bottom: 20px;
}

.current-score, .best-score {
    margin-bottom: 15px;
}

.score-label {
    display: block;
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 5px;
}

.score-value {
    display: block;
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
    transition: all 0.3s ease;
    position: relative;
}

.score-value.score-increase {
    animation: scoreIncrease 0.6s ease-out;
}

.score-value.perfect-score {
    color: #28a745;
    text-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
}

.score-value.good-score {
    color: #ffc107;
    text-shadow: 0 0 8px rgba(255, 193, 7, 0.4);
}

@keyframes scoreIncrease {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
        color: #28a745;
        text-shadow: 0 0 15px rgba(40, 167, 69, 0.8);
    }
    100% {
        transform: scale(1);
    }
}

.score-breakdown h4 {
    margin-bottom: 15px;
    color: #495057;
    text-align: center;
}

.metric {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.metric-name {
    color: #6c757d;
    font-size: 0.9rem;
}

.metric-value {
    font-weight: 600;
    color: #495057;
}

/* 游戏化元素样式 */
.game-stats {
    margin: 15px 0;
    padding: 15px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 10px;
    border: 1px solid #dee2e6;
}

.level-display {
    margin-bottom: 10px;
}

.level-label {
    font-weight: 600;
    color: #495057;
    font-size: 1.1rem;
}

.level-progress {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    margin-top: 5px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
}

.combo-display {
    margin: 10px 0;
    text-align: center;
}

.combo-text {
    display: block;
    font-size: 1.2rem;
    font-weight: bold;
    color: #dc3545;
    margin-bottom: 5px;
    min-height: 1.5rem;
}

.combo-count {
    font-size: 0.9rem;
    color: #6c757d;
}

.stats-row {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
}

.stat-item {
    text-align: center;
    flex: 1;
}

.stat-label {
    display: block;
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 2px;
}

.stat-value {
    display: block;
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
}

.achievements-panel {
    margin-top: 15px;
    padding: 15px;
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    border-radius: 10px;
    border: 1px solid #ffeaa7;
}

.achievements-panel h4 {
    margin: 0 0 10px 0;
    color: #856404;
    font-size: 1rem;
}

.achievements-list {
    min-height: 40px;
}

.no-achievements {
    color: #6c757d;
    font-style: italic;
    font-size: 0.9rem;
}

.achievement-item {
    display: flex;
    align-items: center;
    padding: 8px;
    margin: 5px 0;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    border-left: 4px solid #ffc107;
    animation: achievementPop 0.5s ease-out;
}

.achievement-icon {
    font-size: 1.5rem;
    margin-right: 10px;
}

.achievement-content {
    flex: 1;
}

.achievement-name {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}

.achievement-description {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 2px;
}

@keyframes achievementPop {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 底部 */
.footer {
    text-align: center;
    color: white;
    opacity: 0.8;
    font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .control-panel, .scoring-panel {
        order: 2;
    }
    
    .video-container {
        order: 1;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .header h1 {
        font-size: 2rem;
    }

    .pose-info {
        grid-template-columns: 1fr;
    }
}

/* 自定义动作模态对话框样式 */
.custom-action-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.modal-content {
    background: white;
    border-radius: 15px;
    max-width: 800px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    border-bottom: 1px solid #eee;
}

.modal-header h2 {
    margin: 0;
    color: #333;
    font-size: 1.5rem;
}

.modal-close {
    background: none;
    border: none;
    font-size: 2rem;
    color: #999;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: #666;
}

.modal-body {
    padding: 30px;
}

/* 模式选择样式 */
.mode-selection h3 {
    margin-bottom: 20px;
    color: #333;
}

.mode-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.mode-btn {
    padding: 30px 20px;
    border: 2px solid #ddd;
    border-radius: 12px;
    background: white;
    cursor: pointer;
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    transition: all 0.3s ease;
    text-align: center;
}

.mode-btn:hover {
    border-color: #667eea;
    background: #f8f9ff;
    transform: translateY(-2px);
}

/* 摄像头模式样式 */
.camera-mode h3,
.upload-mode h3,
.edit-mode h3,
.save-mode h3 {
    margin-bottom: 15px;
    color: #333;
}

.camera-controls,
.edit-controls {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.capture-btn,
.detect-pose-btn,
.clear-points-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.capture-btn:hover,
.detect-pose-btn:hover,
.clear-points-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.capture-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.back-btn {
    padding: 10px 16px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: white;
    color: #666;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.back-btn:hover {
    border-color: #999;
    color: #333;
}

/* 上传区域样式 */
.upload-notice {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
    border-left: 4px solid #f39c12;
}

.upload-notice p {
    margin: 5px 0;
    color: #856404;
    font-size: 0.9rem;
    line-height: 1.4;
}

.upload-notice strong {
    font-weight: 600;
}

.upload-area {
    margin-bottom: 20px;
}

.upload-zone {
    border: 2px dashed #ddd;
    border-radius: 12px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fafafa;
}

.upload-zone:hover,
.upload-zone.drag-over {
    border-color: #667eea;
    background: #f8f9ff;
}

.upload-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.upload-zone p {
    margin: 10px 0;
    color: #333;
}

.upload-hint {
    font-size: 0.9rem;
    color: #666 !important;
}

/* 编辑器容器样式 */
.editor-container {
    margin: 20px 0;
    border: 1px solid #ddd;
    border-radius: 8px;
    min-height: 400px;
    background: #f9f9f9;
}

.edit-info {
    margin-top: 15px;
    padding: 15px;
    background: #f0f8ff;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.edit-info p {
    margin: 0 0 10px 0;
    font-weight: 600;
    color: #333;
}

.edit-info ul {
    margin: 0;
    padding-left: 20px;
}

.edit-info li {
    margin-bottom: 5px;
    color: #666;
}

.edit-tips {
    margin-top: 15px;
    padding: 12px;
    background: #fff9e6;
    border-radius: 6px;
    border-left: 3px solid #ffc107;
}

.edit-tips strong {
    color: #856404;
    display: block;
    margin-bottom: 8px;
}

.edit-tips p {
    margin: 0;
    color: #856404;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* 表单样式 */
.action-form {
    max-width: 500px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.form-input,
.form-textarea,
.form-select {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
    outline: none;
    border-color: #667eea;
}

.form-textarea {
    resize: vertical;
    min-height: 80px;
}

.keypoints-summary {
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.keypoints-summary h4 {
    margin: 0 0 10px 0;
    color: #333;
}

.keypoint-count {
    font-weight: bold;
    color: #667eea;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 30px;
}

.save-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.save-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
}

/* 状态信息样式 */
.status {
    padding: 10px 15px;
    border-radius: 6px;
    margin-top: 15px;
    font-weight: 500;
}

.status.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status.loading {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* 自定义动作响应式设计 */
@media (max-width: 768px) {
    .modal-overlay {
        padding: 10px;
    }

    .modal-content {
        max-height: 95vh;
    }

    .modal-body {
        padding: 20px;
    }

    .mode-buttons {
        grid-template-columns: 1fr;
    }

    .camera-controls,
    .edit-controls {
        flex-direction: column;
    }

    .form-actions {
        flex-direction: column;
    }

    .mode-btn {
        padding: 20px 15px;
        font-size: 1rem;
    }
}

/* 自定义动作列表样式 */
.custom-actions {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.custom-actions h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 1rem;
}

.custom-action-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.custom-action-controls .btn {
    flex: 1;
    padding: 8px 12px;
    font-size: 0.9rem;
    margin-bottom: 0;
}

.custom-actions-list {
    max-height: 200px;
    overflow-y: auto;
}

.no-custom-actions {
    color: #6c757d;
    font-style: italic;
    font-size: 0.9rem;
    text-align: center;
    padding: 20px;
    margin: 0;
}

.custom-action-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    margin-bottom: 8px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.custom-action-item:hover {
    background: #e9ecef;
    border-color: #667eea;
}

.action-info {
    flex: 1;
}

.action-name {
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
    margin-bottom: 4px;
}

.action-description {
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 4px;
}

.action-meta {
    font-size: 0.75rem;
    color: #999;
}

.action-controls {
    display: flex;
    gap: 8px;
}

.use-action-btn,
.delete-action-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.use-action-btn {
    background: #667eea;
    color: white;
}

.use-action-btn:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.delete-action-btn {
    background: #dc3545;
    color: white;
}

.delete-action-btn:hover {
    background: #c82333;
    transform: translateY(-1px);
}

/* 自定义动作管理界面样式 */
.custom-action-manager-modal .modal-content.manager-modal {
    max-width: 1000px;
    max-height: 90vh;
}

.manager-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #eee;
    margin-bottom: 20px;
}

.toolbar-left {
    display: flex;
    gap: 20px;
    align-items: center;
}

.actions-count,
.storage-info {
    font-size: 0.9rem;
    color: #666;
}

.toolbar-right {
    display: flex;
    gap: 10px;
}

.import-btn,
.export-btn,
.clear-all-btn {
    padding: 8px 16px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: white;
    color: #333;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.import-btn:hover,
.export-btn:hover {
    border-color: #667eea;
    background: #f8f9ff;
}

.clear-all-btn:hover {
    border-color: #dc3545;
    background: #fff5f5;
    color: #dc3545;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    max-height: 500px;
    overflow-y: auto;
    padding: 10px 0;
}

.no-actions-message {
    grid-column: 1 / -1;
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.no-actions-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.no-actions-message h3 {
    margin: 0 0 10px 0;
    color: #333;
}

.no-actions-message p {
    margin: 0;
    font-size: 0.9rem;
}

.action-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
}

.action-card:hover {
    border-color: #667eea;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
    transform: translateY(-2px);
}

.action-preview {
    height: 150px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.action-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-image {
    font-size: 3rem;
    color: #ccc;
}

.action-details {
    padding: 15px;
}

.action-title {
    margin: 0 0 8px 0;
    font-size: 1rem;
    font-weight: 600;
    color: #333;
}

.action-desc {
    margin: 0 0 10px 0;
    font-size: 0.85rem;
    color: #666;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.action-stats {
    display: flex;
    gap: 15px;
    font-size: 0.8rem;
    color: #999;
}

.action-card-controls {
    display: flex;
    padding: 10px 15px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    gap: 10px;
}

.preview-btn,
.quick-delete-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.preview-btn {
    flex: 1;
    background: #667eea;
    color: white;
}

.preview-btn:hover {
    background: #5a6fd8;
}

.quick-delete-btn {
    background: #dc3545;
    color: white;
    width: 32px;
    padding: 6px;
}

.quick-delete-btn:hover {
    background: #c82333;
}

.modal-footer {
    padding: 20px 30px;
    border-top: 1px solid #eee;
    text-align: right;
}

.close-manager-btn {
    padding: 10px 20px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: white;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
}

.close-manager-btn:hover {
    border-color: #999;
    color: #333;
}

/* 预览模态框样式 */
.preview-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1001;
    background: rgba(0, 0, 0, 0.8);
}

.preview-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.preview-content {
    background: white;
    border-radius: 15px;
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    border-bottom: 1px solid #eee;
}

.preview-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.3rem;
}

.preview-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #999;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.preview-close:hover {
    color: #666;
}

.preview-body {
    padding: 30px;
}

.preview-image-container {
    text-align: center;
    margin-bottom: 30px;
}

.preview-image-container img {
    max-width: 100%;
    max-height: 300px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.preview-info {
    display: grid;
    gap: 15px;
}

.info-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
    border-bottom: none;
}

.info-item label {
    font-weight: 600;
    color: #333;
    min-width: 120px;
    margin-right: 15px;
}

.info-item span {
    color: #666;
    flex: 1;
}

.preview-actions {
    display: flex;
    gap: 15px;
    padding: 20px 30px;
    border-top: 1px solid #eee;
    justify-content: center;
}

.edit-action-btn,
.duplicate-action-btn,
.delete-preview-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.edit-action-btn {
    background: #667eea;
    color: white;
}

.edit-action-btn:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.duplicate-action-btn {
    background: #28a745;
    color: white;
}

.duplicate-action-btn:hover {
    background: #218838;
    transform: translateY(-1px);
}

.delete-preview-btn {
    background: #dc3545;
    color: white;
}

.delete-preview-btn:hover {
    background: #c82333;
    transform: translateY(-1px);
}

/* 管理界面响应式设计 */
@media (max-width: 768px) {
    .custom-action-manager-modal .modal-content.manager-modal {
        max-width: 95vw;
        margin: 10px;
    }

    .manager-toolbar {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .toolbar-left {
        justify-content: center;
    }

    .toolbar-right {
        justify-content: center;
    }

    .actions-grid {
        grid-template-columns: 1fr;
        max-height: 400px;
    }

    .preview-content {
        max-width: 95vw;
        margin: 10px;
    }

    .preview-actions {
        flex-direction: column;
    }
}
